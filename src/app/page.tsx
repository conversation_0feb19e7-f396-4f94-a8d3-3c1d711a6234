"use client";
import { useState, useEffect } from "react";
import BridgeInterface from "@/components/BridgeInterface";
import BackgroundCoins from "@/components/BackgroundCoins";
import Providers from "@/components/Providers";

export default function Home() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null;

  return (
    <Providers>
      <div className="min-h-screen bg-black flex items-center justify-center p-4 sm:p-6 lg:p-8 relative">
        <BackgroundCoins count={80} />
        <div className="w-full max-w-md relative z-20">
          <BridgeInterface />
        </div>
      </div>
    </Providers>
  );
}
