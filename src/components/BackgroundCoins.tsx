"use client";
import React, { useState, useEffect, useMemo, useCallback } from 'react';

interface Coin {
  id: number;
  x: number;
  y: number;
  size: number;
  rotation: number;
  originalX: number;
  originalY: number;
}

interface BackgroundCoinsProps {
  count?: number;
}

const BackgroundCoins = React.memo(function BackgroundCoins({ count = 100 }: BackgroundCoinsProps) {
  const [coins, setCoins] = useState<Coin[]>([]);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isClient, setIsClient] = useState(false);

  // Check for overlapping coins
  const isOverlapping = useCallback((newCoin: Coin, existingCoins: Coin[]): boolean => {
    return existingCoins.some(coin => {
      const distance = Math.sqrt(
        Math.pow(newCoin.x - coin.x, 2) + Math.pow(newCoin.y - coin.y, 2)
      );
      const minDistance = (newCoin.size + coin.size) / 2 + 20; // 20px buffer
      return distance < minDistance;
    });
  }, []);

  // Generate random coins without overlapping
  const generateCoins = useCallback(() => {
    const newCoins: Coin[] = [];
    const maxAttempts = count * 10; // Prevent infinite loops
    let attempts = 0;

    for (let i = 0; i < count && attempts < maxAttempts; i++) {
      let coin: Coin;
      let validPosition = false;
      let positionAttempts = 0;

      do {
        const size = Math.random() * 80 + 30; // 30px to 110px for larger variety
        coin = {
          id: i,
          x: Math.random() * (window.innerWidth - size),
          y: Math.random() * (window.innerHeight - size),
          size,
          rotation: Math.random() * 360,
          originalX: 0,
          originalY: 0,
        };
        coin.originalX = coin.x;
        coin.originalY = coin.y;

        validPosition = !isOverlapping(coin, newCoins);
        positionAttempts++;
      } while (!validPosition && positionAttempts < 50);

      if (validPosition) {
        newCoins.push(coin);
      }
      attempts++;
    }

    return newCoins;
  }, [count, isOverlapping]);

  // Initialize coins on client side
  useEffect(() => {
    setIsClient(true);
    if (typeof window !== 'undefined') {
      setCoins(generateCoins());
    }
  }, [generateCoins]);

  // Handle window resize
  useEffect(() => {
    if (!isClient) return;

    const handleResize = () => {
      setCoins(generateCoins());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isClient, generateCoins]);

  // Debounced mouse move handler
  const handleMouseMove = useCallback((e: MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  }, []);

  useEffect(() => {
    if (!isClient) return;

    let timeoutId: NodeJS.Timeout;
    const debouncedMouseMove = (e: MouseEvent) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => handleMouseMove(e), 16); // ~60fps
    };

    window.addEventListener('mousemove', debouncedMouseMove);
    return () => {
      window.removeEventListener('mousemove', debouncedMouseMove);
      clearTimeout(timeoutId);
    };
  }, [isClient, handleMouseMove]);

  // Calculate coin positions based on mouse proximity
  const coinPositions = useMemo(() => {
    return coins.map(coin => {
      const distance = Math.sqrt(
        Math.pow(mousePosition.x - coin.originalX - coin.size / 2, 2) +
        Math.pow(mousePosition.y - coin.originalY - coin.size / 2, 2)
      );

      const maxDistance = 150; // Maximum distance for interaction
      const maxOffset = 30; // Maximum offset in pixels

      if (distance < maxDistance) {
        const force = (maxDistance - distance) / maxDistance;
        const angle = Math.atan2(
          coin.originalY + coin.size / 2 - mousePosition.y,
          coin.originalX + coin.size / 2 - mousePosition.x
        );

        const offsetX = Math.cos(angle) * force * maxOffset;
        const offsetY = Math.sin(angle) * force * maxOffset;

        return {
          ...coin,
          x: coin.originalX + offsetX,
          y: coin.originalY + offsetY,
        };
      }

      return coin;
    });
  }, [coins, mousePosition]);

  if (!isClient) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden" style={{ zIndex: 1 }}>
      {coinPositions.map((coin) => (
        <div
          key={coin.id}
          className="absolute transition-all duration-300 ease-out will-change-transform"
          style={{
            left: `${coin.x}px`,
            top: `${coin.y}px`,
            width: `${coin.size}px`,
            height: `${coin.size}px`,
            transform: `rotate(${coin.rotation}deg) translate3d(0, 0, 0)`,
            filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
          }}
        >
          <img
            src="/coin.webp"
            alt=""
            className="w-full h-full object-contain opacity-60"
            draggable={false}
            style={{
              imageRendering: 'crisp-edges',
            }}
          />
        </div>
      ))}
    </div>
  );
});

export default BackgroundCoins;
